@extends('site.index')
@section('content')

<title>
{{app()->getLocale() == 'ar' ?$Def->Name :$Def->NameEn}}
</title>


  <!--==============================
    slider Area
    ==============================-->
    <div class="hero-wrapper hero-1" id="hero">
        <div class="hero-slider-manual" id="heroSlider1">


               @forelse($Webslider as $slide)
            <div class="hero-slider" style="background-image: url({{URL::to($slide->Image)}}); background-size: cover; background-position: center; background-repeat: no-repeat;">
                <div class="hero-shape1 shape-mockup movingX" data-bottom="165px" data-right="0">
                    <img src="assets/img/" alt="img">
                </div>
                <div class="container">
                    <div class="row sectionOne">
                        <div class="col-xl-6 col-lg-7 col-md-9">
                            <div class="hero-style1">
                                <span class="hero-subtitle" data-ani="slideinup" data-ani-delay="0s">{{app()->getLocale() == 'ar' ?$slide->Arabic_Title :$slide->English_Title}}</span>
                                <h1 class="hero-title text-white" data-ani="slideinup" data-ani-delay="0.1s">
                                     {{app()->getLocale() == 'ar' ?$slide->Arabic_Desc :$slide->English_Desc}}
                                </h1>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @empty
            <!-- Fallback slide if no sliders found -->
            <div class="hero-slider" style="background-image: url('assets/img/default-slider.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat;">
                <div class="container">
                    <div class="row sectionOne">
                        <div class="col-xl-6 col-lg-7 col-md-9">
                            <div class="hero-style1">
                                <span class="hero-subtitle" data-ani="slideinup" data-ani-delay="0s">Welcome</span>
                                <h1 class="hero-title text-white" data-ani="slideinup" data-ani-delay="0.1s">
                                     No sliders found
                                </h1>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforelse

        </div>
        <div class="hero-arrow">
            <button data-slick-prev="#heroSlider1" class="slick-arrow slick-prev">PREV</button>
            <button data-slick-next="#heroSlider1" class="slick-arrow slick-next">NEXT</button>
        </div>
    </div>
    <!--======== / Hero Section ========-->

    <!--==============================
    feature Area
    ==============================-->
    <div class="space">
        <div class="container">
            <div class="feature-area">
                <div class="row gx-0">

                  @foreach($Features as $feat)


                    <div class="col-lg-3">
                        <div class="feature-card">
                            <div class="feature-card_bg">
                                <img src="{{asset('Front/assets/img/bg/feature-card_bg1.png')}}" alt="img">

                            </div>
                            <div class="feature-card_icon">
                                <img src="{{URL::to($feat->Icon)}}" alt="img">

                            </div>
                            <h6 class="feature-card_subtitle">

                    {{app()->getLocale() == 'ar' ?$feat->Arabic_Title :$feat->English_Title}}
                            </h6>


                        </div>
                    </div>
                 @endforeach
                </div>
            </div>
        </div>
    </div>

    <!--==============================
    About Area
    ==============================-->
    <div class="space-bottom" id="About">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <div class="about-thumb mb-5 mb-lg-0">
                        <img class="about-img-1" src=" {{URL::to($About->Image)}}" alt="img">
                        <img class="about-img-2 jump" src="{{asset('Front/assets/img/aboutt.png')}}" alt="img">
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="about-content-wrap">
                        <div class="title-area mb-0">
                            <span class="sub-title">{{trans('admin.About')}}</span>
                            <h2 class="sec-title"> {{app()->getLocale() == 'ar' ?$About->Arabic_Title :$About->English_Title}}  </h2>
                            <p class="sec-text">     {{app()->getLocale() == 'ar' ?$About->Arabic_Desc :$About->English_Desc}}
                            </p>

                        </div>
                        <div class="btn-wrap mt-40">

                            <div class="about-info-wrap">
                                <div class="icon"><i class="fas fa-phone-volume"></i></div>
                                <div class="details">
                                    <p class="about-info-title">{{trans('admin.NeedHelp?')}}</p>
                                    <a class="about-info-link" href="tel:{{$Footer->Phone}}">{{$Footer->Phone}}</a>
                                </div>
                            </div>





                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>




    <!--==============================
    Shop Area
    ==============================-->
             <div class="ShopArea">
                <div class="row justify-content-center">
                    <div class="col-xl-6 col-lg-8 text-center">
                        <div class="title-area text-center">
                            <span class="sub-title text-theme2">{{trans('admin.OurGoods')}}</span>

                        </div>
                    </div>
                </div>
                <div class="row global-carousel" id="productCarousel" data-slide-show="4" data-lg-slide-show="4" data-md-slide-show="3" data-sm-slide-show="2" data-xs-slide-show="1">

                          @foreach($Products as $pro)
                    <div class="col-lg-3 col-md-6">
                        <div class="product-card">
                            <div class="product-img">
                                <img src="{{URL::to($pro->Image)}}" alt="Product Image">
                                <div class="actions">
                                    <a href="{{url('ProDetails/'.$pro->id)}}" class="btn style2"><i class="fal fa-shopping-cart"></i></a>
                                    <a href="{{url('PrescriptionPro/'.$pro->id)}}" class="btn style2"><i class="fas fa-file"></i></a>

                                </div>


                                                         @if($pro->Type == 0)


                                   <div class="product-tag"> {{trans('admin.Recently')}}</div>
                                          @elseif($pro->Type == 1)

                                   <div class="product-tag"> {{trans('admin.Special')}}</div>
                                          @elseif($pro->Type == 2)


                                   <div class="product-tag">{{trans('admin.Finaly')}}</div>

                                         @endif
                            </div>
                            <div class="product-content">
                                <h3 class="product-title"><a href="{{url('ProDetails/'.$pro->id)}}"> {{app()->getLocale() == 'ar' ?$pro->P_Ar_Name :$pro->P_En_Name}}</a></h3>

                                   @if(!empty($pro->Offer_Price))
                                <span class="price"><del>{{$pro->Price}}</del>{{$pro->Offer_Price}} {{$pro->Symbol}}</span>
                                     @else
                                   <span class="price">{{$pro->Price}} {{$pro->Symbol}}</span>
                                    @endif
                            </div>
                        </div>
                    </div>
                        @endforeach
                </div>
            </div>



    <!--==============================
    Service Area
    ==============================-->
    <div class="service-bg-area" data-bg-src="{{asset('Front/assets/img/bg/service-bg.png')}}">
        <div class="sec-shape-top">
            <img src="{{asset('Front/assets/img/bg/sec-shape-top.png')}}" alt="img">
        </div>
        <!--==============================
        Service Area 01
        ==============================-->
        <div class="service-area-1  overflow-hidden">
            <div class="container">
                <div class="title-area text-center">
                <span class="sub-title">{{trans('admin.Services')}}</span>

            </div>
            </div>
            <div class="container-fluid p-0">
                <div class="row global-carousel service-slider-1" data-slide-show="4" data-ml-slide-show="3" data-lg-slide-show="3" data-md-slide-show="2" data-sm-slide-show="1" data-xs-slide-show="1" data-dots="false">




   @foreach($Services as $serv)

                    <div class="col-lg-4 col-md-6">
                        <div class="service-card">
                            <div class="service-card_icon">
                                <img src="{{URL::to($serv->Icon)}}" alt="img">
                            </div>
                            <div class="service-card_content">
                                <h4 class="service-card_title h5"><a href="#">{{app()->getLocale() == 'ar' ?$serv->Arabic_Title :$serv->English_Title}}   </a></h4>
                                <p class="service-card_text">  {{app()->getLocale() == 'ar' ?$serv->Arabic_Desc :$serv->English_Desc}}  </p>

                            </div>
                        </div>
                    </div>

                  @endforeach

                </div>
            </div>
        </div>

        <div class="sec-shape-bottom">
            <img src="{{asset('Front/assets/img/bg/sec-shape-bottom.png')}}" alt="img">
        </div>
    </div>




    <!-----portfolio start----->
       <div class="portfolio-area-1" style="background-image:url({{asset('Front/assets/img/bg/portfolio-bg-1.png')}}); background-size: cover;">

        <div class="container">
            <div class="title-area text-center">
                <span class="sub-title"> {{trans('admin.Portfilio')}}</span>

            </div>
        </div>
        <div class="container-fluid">
            <div class="flip-gallery-area">
                <div class="flip-gallery">
                    <ul class="flip-items">

    @foreach($Gallery as $gal)

                        <li>
                            <div class="gallery-card gallery-flip">
                                <div class="gallery-img">
                                    <img src="{{URL::to($gal->Image)}}" alt="gallery image">
                                </div>
                                <div class="gallery-content">
                                    <div class="media-left">
                                        <h6 class="gallery-content_subtitle">

                                          @if(!empty($gal->Category()->first()->Arabic_Name))

                           {{app()->getLocale() == 'ar' ?$gal->Category()->first()->Arabic_Name :$gal->Category()->first()->English_Name}}
                                            @endif
                                        </h6>
                                        <h4 class="gallery-content_title">{{app()->getLocale() == 'ar' ?$gal->Arabic_Name :$gal->English_Name}}</h4>
                                    </div>
                                    <a href="{{$gal->Links}}" class="icon-btn popup-image">
                                        <i class="far fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                        </li>

                           @endforeach


                    </ul>
                </div>
            </div>
        </div>
    </div>
    <!-----portfolio end----->


        <!--==============================
    whu choose us Area
    ==============================-->

    <div class="goal-area-2 space">
        <div class="container">
            <div class="row justify-content-between">
                <div class="col-xl-6 align-self-center order-xl-2">
                    <div class=" mb-xl-0 mb-40">
                        <img src="{{URL::to($HowWeWork->Image)}}" alt="img">
                    </div>
                </div>
                <div class="col-xl-6 order-xl-1">
                    <div class="title-area">
                        <span class="sub-title style2"> {{trans('admin.WhyChoose')}}</span>
                        <h2 class="sec-title fw-bold">{{app()->getLocale() == 'ar' ?$HowWeWork->Arabic_Title :$HowWeWork->English_Title}}</h2>

                        <div class="about-grid-wrap style3 mt-40">
                            <div class="about-grid style3">
                                <div class="about-grid_icon">
                                    <img src="{{URL::to($HowWeWork1->Icon)}}" alt="img">
                                </div>
                                <div class="about-grid_content">

                                    <p class="about-grid_text">{{app()->getLocale() == 'ar' ?$HowWeWork1->Arabic_Title :$HowWeWork1->English_Title}}</p>
                                </div>
                            </div>
                            <div class="about-grid style3">
                                <div class="about-grid_icon">
                           <img src="{{URL::to($HowWeWork2->Icon)}}" alt="img">

                                </div>
                                <div class="about-grid_content">

                                    <p class="about-grid_text">{{app()->getLocale() == 'ar' ?$HowWeWork2->Arabic_Title :$HowWeWork2->English_Title}}</p>
                                </div>
                            </div>
                                 <div class="about-grid style3">
                                <div class="about-grid_icon">
                                    <img src="{{URL::to($HowWeWork3->Icon)}}" alt="img">

                                </div>
                                <div class="about-grid_content">

                                    <p class="about-grid_text">{{app()->getLocale() == 'ar' ?$HowWeWork3->Arabic_Title :$HowWeWork3->English_Title}}</p>
                                </div>
                            </div>
                            <div class="about-grid style3">
                                <div class="about-grid_icon">
                                    <img src="{{URL::to($HowWeWork4->Icon)}}" alt="img">

                                </div>
                                <div class="about-grid_content">

                                    <p class="about-grid_text">{{app()->getLocale() == 'ar' ?$HowWeWork4->Arabic_Title :$HowWeWork4->English_Title}}</p>
                                </div>
                            </div>


                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

      <!--==============================



    Testimonial Area 02
    ==============================-->
    <div class="testimonial-area-2  overflow-hidden">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <div class="title-area text-center">
                        <span class="sub-title style2">S {{trans('admin.Testimonial')}} </span>

                    </div>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="testi-box-wrap2 text-center">
                        <div class="row global-carousel testi-slider-2" id="testiSlider2" data-slide-show="1">

   @foreach($Testiminoals as $testi)
                            <div class="col-lg-6">
                                <div class="testi-box style2">
                                    <div class="testi-box_thumb">
                                        <img src="{{URL::to($testi->Image)}}" alt="img">
                                    </div>
                                    <div class="testi-box_content">
                                        <p class="testi-box_text"> {{app()->getLocale() == 'ar' ?$testi->Arabic_Desc :$testi->English_Desc}}   </p>
                                    </div>
                                    <div class="testi-box_profile">
                                        <h4 class="testi-box_name">  {{app()->getLocale() == 'ar' ?$testi->Arabic_Name :$testi->English_Name}}</h4>
                                        <span class="testi-box_desig">{{app()->getLocale() == 'ar' ?$testi->Arabic_Job :$testi->English_Job}} </span>
                                    </div>
                                </div>
                            </div>
                             @endforeach
                        </div>
                        <div class="testi-arrow">
                            <button data-slick-prev="#testiSlider2" class="slick-arrow slick-prev"><i class="fa-light fa-arrow-left"></i></button>
                            <button data-slick-next="#testiSlider2" class="slick-arrow slick-next"><i class="fa-light fa-arrow-right"></i></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!--==============================
    Blog Area
    ==============================-->
    <section class="blog-area mt-50 mb-50 bg-smoke3">
        <div class="container">
            <div class="title-area text-center" style="padding: 20px">
                <span class="sub-title">
                     {{trans('admin.Blogs')}}
                </span>

            </div>
            <div class="row global-carousel blog-slider" data-slide-show="3" data-lg-slide-show="2" data-md-slide-show="2" data-sm-slide-show="1" data-xs-slide-show="1" data-dots="false" data-md-dots="true">



     @foreach($Articles as $art)


                <div class="col-md-6 col-lg-4">
                    <div class="blog-card">
                        <div class="blog-img">
                            <img src="{{URL::to($art->Sub_Image)}}" alt="blog image">
                        </div>
                        <div class="blog-content" data-bg-src="{{asset('Front/assets/img/blog/blog_card1_bg.png')}}">
                            <div class="blog-meta">
                                <a href="{{url('BlogsDet/'.$art->id)}}"><i class="fal fa-calendar"></i>{{$art->Date}}</a>
                                <a href="{{url('BlogsDet/'.$art->id)}}"><i class="far fa-user"></i>{{$art->Author}}</a>
                            </div>
                            <h3 class="blog-title box-title"><a href="{{url('BlogsDet/'.$art->id)}}">  {{app()->getLocale() == 'ar' ?$art->Arabic_Title :$art->English_Title}}  </a></h3>

                        </div>
                    </div>
                </div>

                @endforeach

            </div>
        </div>
    </section>

@push('scripts')
<script>
// Fixed slider initialization
function initHeroSlider() {
    if (typeof jQuery === 'undefined') {
        console.log('jQuery not loaded, retrying...');
        setTimeout(initHeroSlider, 500);
        return;
    }

    var $ = jQuery;
    var $slider = $('#heroSlider1');

    console.log('=== SLIDER INITIALIZATION ===');
    console.log('Slider element exists:', $slider.length > 0);

    if ($slider.length === 0) {
        console.log('Slider element not found');
        return;
    }

    // Count actual slide divs (not slick-generated elements)
    var $slides = $slider.children('.hero-slider');
    console.log('Hero-slider children count:', $slides.length);

    // Log each slide
    $slides.each(function(index) {
        console.log('Slide ' + (index + 1) + ' found');
    });

    // Destroy existing slick if present
    if ($slider.hasClass('slick-initialized')) {
        console.log('Destroying existing slick...');
        $slider.slick('unslick');
    }

    // Only initialize if we have slides
    if ($slides.length > 0) {
        console.log('Initializing slider with ' + $slides.length + ' slides...');

        $slider.slick({
            autoplay: true,
            autoplaySpeed: 4000,
            fade: true,
            arrows: true,
            dots: true,
            infinite: $slides.length > 1, // Only infinite if more than 1 slide
            slidesToShow: 1,
            slidesToScroll: 1,
            pauseOnHover: false,
            pauseOnFocus: false,
            adaptiveHeight: true,
            prevArrow: '<button type="button" class="slick-prev"><i class="far fa-arrow-left"></i></button>',
            nextArrow: '<button type="button" class="slick-next"><i class="far fa-arrow-right"></i></button>'
        });

        console.log('Slider initialized successfully!');

        // Add arrow wrapper class for styling
        $slider.closest('.container').parent().addClass('arrow-wrap');

    } else {
        console.log('No slides found to initialize');
    }
}

// Initialize when DOM is ready
$(document).ready(function() {
    // Wait a bit for all content to load
    setTimeout(initHeroSlider, 1000);
});

// Fallback initialization
$(window).on('load', function() {
    setTimeout(initHeroSlider, 2000);
});
</script>
@endpush


@endsection
